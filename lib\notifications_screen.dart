import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class NotificationItem {
  final String title;
  final String subtitle;
  final DateTime time;
  final IconData icon;
  final Color iconBg;

  NotificationItem({
    required this.title,
    required this.subtitle,
    required this.time,
    required this.icon,
    required this.iconBg,
  });
}

class NotificationsScreen extends StatelessWidget {
  NotificationsScreen({super.key});

  final List<NotificationItem> notifications = [
    NotificationItem(
      title: "New message from <PERSON><PERSON>",
      subtitle: "Hey! Are you available for guitar tutoring tomorrow?",
      time: DateTime.now().subtract(Duration(minutes: 10)),
      icon: Icons.message,
      iconBg: Colors.deepPurple,
    ),
    NotificationItem(
      title: "Session request from <PERSON><PERSON>",
      subtitle: "Requested to schedule a math tutoring session.",
      time: DateTime.now().subtract(Duration(hours: 2)),
      icon: Icons.calendar_today,
      iconBg: Colors.green,
    ),
    NotificationItem(
      title: "Review received",
      subtitle: "You received a 5-star rating from <PERSON><PERSON>.",
      time: DateTime.now().subtract(Duration(days: 1, hours: 3)),
      icon: Icons.star,
      iconBg: Colors.amber,
    ),
  ];

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 60) {
      return "${diff.inMinutes} min ago";
    } else if (diff.inHours < 24) {
      return "${diff.inHours} hrs ago";
    } else {
      return DateFormat.yMMMd().format(time);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Notifications"),
        backgroundColor: Colors.deepPurple,
      ),
      body: ListView.separated(
        itemCount: notifications.length,
        separatorBuilder: (_, __) => Divider(height: 1),
        itemBuilder: (context, index) {
          final n = notifications[index];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: n.iconBg,
              child: Icon(n.icon, color: Colors.white),
            ),
            title: Text(n.title),
            subtitle: Text(n.subtitle, maxLines: 2, overflow: TextOverflow.ellipsis),
            trailing: Text(
              _formatTime(n.time),
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            onTap: () {
              // For frontend only, just show a snackbar
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Clicked: ${n.title}")),
              );
            },
          );
        },
      ),
    );
  }
}
