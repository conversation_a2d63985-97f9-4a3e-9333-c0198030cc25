import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class PostOfferRequestScreen extends StatefulWidget {
  const PostOfferRequestScreen({super.key});

  @override
  State<PostOfferRequestScreen> createState() => _PostOfferRequestScreenState();
}

class _PostOfferRequestScreenState extends State<PostOfferRequestScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  // Tab Controller for Offer/Request tabs
  late TabController _tabController;

  // Form keys for both tabs
  final _offerFormKey = GlobalKey<FormState>();
  final _requestFormKey = GlobalKey<FormState>();

  // Controllers for Offer form
  final _offerTitleController = TextEditingController();
  final _offerDescController = TextEditingController();
  final _offerDurationController = TextEditingController();
  final _offerLocationController = TextEditingController();

  // Controllers for Request form
  final _requestTitleController = TextEditingController();
  final _requestDescController = TextEditingController();
  final _requestDurationController = TextEditingController();
  final _requestLocationController = TextEditingController();

  // Dropdown values for Offer
  String? _offerSelectedCategory;
  String? _offerSelectedMode;
  String? _offerSelectedLevel;

  // Dropdown values for Request
  String? _requestSelectedCategory;
  String? _requestSelectedMode;
  String? _requestSelectedLevel;

  // Loading and animation states
  bool _isLoading = false;
  late AnimationController _loadingAnimationController;
  late Animation<double> _loadingAnimation;

  // Skill categories as specified in requirements
  final List<String> _categories = [
    'Art',
    'Music',
    'Coding',
    'Language',
    'Math',
    'Science',
    'Business',
    'Writing',
    'Photography',
    'Cooking',
    'Sports',
    'Other',
  ];

  final List<String> _modes = ['Online', 'In-person', 'Hybrid'];
  final List<String> _levels = ['Beginner', 'Intermediate', 'Expert'];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadingAnimationController = AnimationController(duration: const Duration(milliseconds: 1500), vsync: this);
    _loadingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _loadingAnimationController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _loadingAnimationController.dispose();

    // Dispose Offer controllers
    _offerTitleController.dispose();
    _offerDescController.dispose();
    _offerDurationController.dispose();
    _offerLocationController.dispose();

    // Dispose Request controllers
    _requestTitleController.dispose();
    _requestDescController.dispose();
    _requestDurationController.dispose();
    _requestLocationController.dispose();

    super.dispose();
  }

  void _showSnackbar(String message, [Color? color]) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color ?? Colors.deepPurple,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  // Validation functions
  String? _validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Title is required';
    }
    if (value.trim().length < 3) {
      return 'Title must be at least 3 characters';
    }
    return null;
  }

  String? _validateDescription(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Description is required';
    }
    return null;
  }

  String? _validateDuration(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Duration is required';
    }
    // Check if it contains a number and unit
    final regex = RegExp(r'^\d+\s*(hour|hours|week|weeks|day|days|minute|minutes)$', caseSensitive: false);
    if (!regex.hasMatch(value.trim())) {
      return 'Format: "2 hours" or "3 weeks"';
    }
    return null;
  }

  String? _validateLocation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Location is required';
    }
    return null;
  }

  String? _validateDropdown(String? value, String fieldName) {
    if (value == null) {
      return '$fieldName is required';
    }
    return null;
  }

  // Submit functions for both tabs
  Future<void> _submitOffer() async {
    if (!_offerFormKey.currentState!.validate()) return;
    await _submitToFirestore(true);
  }

  Future<void> _submitRequest() async {
    if (!_requestFormKey.currentState!.validate()) return;
    await _submitToFirestore(false);
  }

  Future<void> _submitToFirestore(bool isOffer) async {
    setState(() {
      _isLoading = true;
      _loadingAnimationController.repeat();
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showSnackbar('You must be logged in to post.', Colors.red);
        return;
      }

      // Get the appropriate controllers based on tab
      final titleController = isOffer ? _offerTitleController : _requestTitleController;
      final descController = isOffer ? _offerDescController : _requestDescController;
      final durationController = isOffer ? _offerDurationController : _requestDurationController;
      final locationController = isOffer ? _offerLocationController : _requestLocationController;
      final selectedCategory = isOffer ? _offerSelectedCategory : _requestSelectedCategory;
      final selectedMode = isOffer ? _offerSelectedMode : _requestSelectedMode;
      final selectedLevel = isOffer ? _offerSelectedLevel : _requestSelectedLevel;

      final data = {
        'type': isOffer ? 'offer' : 'request',
        'title': titleController.text.trim(),
        'description': descController.text.trim(),
        'category': selectedCategory,
        'duration': durationController.text.trim(),
        'location': locationController.text.trim(),
        'mode': selectedMode,
        'experienceLevel': selectedLevel,
        'userId': user.uid,
        'userEmail': user.email,
        'timestamp': FieldValue.serverTimestamp(),
      };

      // Save to 'skills' collection as specified in requirements
      await FirebaseFirestore.instance.collection('skills').add(data);

      _showSnackbar(
        isOffer ? 'Skill Offer Posted Successfully!' : 'Skill Request Submitted Successfully!',
        Colors.green,
      );

      // Reset form after successful submission
      _resetForm(isOffer);

      // Navigate to home instead of popping
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      debugPrint('Error submitting to Firestore: $e');
      _showSnackbar('Failed to submit. Please try again.', Colors.red);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
        _loadingAnimationController.stop();
        _loadingAnimationController.reset();
      }
    }
  }

  void _resetForm(bool isOffer) {
    if (isOffer) {
      _offerTitleController.clear();
      _offerDescController.clear();
      _offerDurationController.clear();
      _offerLocationController.clear();
      setState(() {
        _offerSelectedCategory = null;
        _offerSelectedMode = null;
        _offerSelectedLevel = null;
      });
    } else {
      _requestTitleController.clear();
      _requestDescController.clear();
      _requestDurationController.clear();
      _requestLocationController.clear();
      setState(() {
        _requestSelectedCategory = null;
        _requestSelectedMode = null;
        _requestSelectedLevel = null;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text('Post Skill'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.school), text: 'I can teach...'),
            Tab(icon: Icon(Icons.help_outline), text: 'I want to learn...'),
          ],
        ),
      ),
      body:
          _isLoading
              ? _buildLoadingOverlay()
              : TabBarView(
                controller: _tabController,
                children: [_buildOfferForm(isDarkMode), _buildRequestForm(isDarkMode)],
              ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: 0.8 + (_loadingAnimation.value * 0.4),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurple),
                    strokeWidth: 4,
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            const Text(
              'Posting your skill...',
              style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOfferForm(bool isDarkMode) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _offerFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Share your expertise with others',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            _buildFormField(
              controller: _offerTitleController,
              label: 'Title',
              hint: 'e.g., "Learn Python Programming"',
              icon: Icons.title,
              validator: _validateTitle,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildFormField(
              controller: _offerDescController,
              label: 'Description',
              hint: 'Describe what you can teach and your approach',
              icon: Icons.description,
              validator: _validateDescription,
              maxLines: 4,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _offerSelectedCategory,
              items: _categories,
              label: 'Category',
              icon: Icons.category,
              onChanged: (value) => setState(() => _offerSelectedCategory = value),
              validator: (value) => _validateDropdown(value, 'Category'),
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildFormField(
              controller: _offerDurationController,
              label: 'Duration',
              hint: 'e.g., "2 hours", "3 weeks"',
              icon: Icons.timer,
              validator: _validateDuration,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildFormField(
              controller: _offerLocationController,
              label: 'Location',
              hint: 'e.g., "Online", "New York", "My place"',
              icon: Icons.location_on,
              validator: _validateLocation,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _offerSelectedMode,
              items: _modes,
              label: 'Mode',
              icon: Icons.laptop,
              onChanged: (value) => setState(() => _offerSelectedMode = value),
              validator: (value) => _validateDropdown(value, 'Mode'),
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _offerSelectedLevel,
              items: _levels,
              label: 'Experience Level',
              icon: Icons.star,
              onChanged: (value) => setState(() => _offerSelectedLevel = value),
              validator: (value) => _validateDropdown(value, 'Experience Level'),
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.school),
                label: const Text('Post Offer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                onPressed: _submitOffer,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequestForm(bool isDarkMode) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _requestFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'What would you like to learn?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            _buildFormField(
              controller: _requestTitleController,
              label: 'Title',
              hint: 'e.g., "Looking for Python tutor"',
              icon: Icons.title,
              validator: _validateTitle,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildFormField(
              controller: _requestDescController,
              label: 'Description',
              hint: 'Describe what you want to learn and your current level',
              icon: Icons.description,
              validator: _validateDescription,
              maxLines: 4,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _requestSelectedCategory,
              items: _categories,
              label: 'Category',
              icon: Icons.category,
              onChanged: (value) => setState(() => _requestSelectedCategory = value),
              validator: (value) => _validateDropdown(value, 'Category'),
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildFormField(
              controller: _requestDurationController,
              label: 'Duration',
              hint: 'e.g., "2 hours", "3 weeks"',
              icon: Icons.timer,
              validator: _validateDuration,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildFormField(
              controller: _requestLocationController,
              label: 'Location',
              hint: 'e.g., "Online", "New York", "My place"',
              icon: Icons.location_on,
              validator: _validateLocation,
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _requestSelectedMode,
              items: _modes,
              label: 'Mode',
              icon: Icons.laptop,
              onChanged: (value) => setState(() => _requestSelectedMode = value),
              validator: (value) => _validateDropdown(value, 'Mode'),
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 16),
            _buildDropdownField(
              value: _requestSelectedLevel,
              items: _levels,
              label: 'Experience Level',
              icon: Icons.star,
              onChanged: (value) => setState(() => _requestSelectedLevel = value),
              validator: (value) => _validateDropdown(value, 'Experience Level'),
              isDarkMode: isDarkMode,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.help_outline),
                label: const Text('Post Request'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                onPressed: _submitRequest,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required String? Function(String?) validator,
    required bool isDarkMode,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      validator: validator,
      style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.deepPurple),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.deepPurple, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
        labelStyle: TextStyle(color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700),
        hintStyle: TextStyle(color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500),
      ),
    );
  }

  Widget _buildDropdownField({
    required String? value,
    required List<String> items,
    required String label,
    required IconData icon,
    required void Function(String?) onChanged,
    required String? Function(String?) validator,
    required bool isDarkMode,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      items: items.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
      onChanged: onChanged,
      validator: validator,
      style: TextStyle(color: isDarkMode ? Colors.white : Colors.black87),
      dropdownColor: isDarkMode ? Colors.grey.shade800 : Colors.white,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Colors.deepPurple),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.deepPurple, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade50,
        labelStyle: TextStyle(color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700),
      ),
    );
  }
}
