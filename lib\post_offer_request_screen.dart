import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class PostOfferRequestScreen extends StatefulWidget {
  const PostOfferRequestScreen({super.key});

  @override
  State<PostOfferRequestScreen> createState() => _PostOfferRequestScreenState();
}

class _PostOfferRequestScreenState extends State<PostOfferRequestScreen> with AutomaticKeepAliveClientMixin {
  bool isOffer = true;
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descController = TextEditingController();
  final _durationController = TextEditingController();
  final _locationController = TextEditingController();
  String? _selectedCategory;
  String? _selectedExchange;
  String? _selectedMode;
  String? _selectedLevel;
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  bool _isLoading = false;

  final List<String> _categories = ['Programming', 'Language', 'Art', 'Music', 'Math', 'Science', 'Business', 'Other'];
  final List<String> _exchangeOptions = ['Barter', 'Free', 'Token/Credit-based'];
  final List<String> _modes = ['Online', 'Offline', 'Hybrid'];
  final List<String> _levels = ['Beginner', 'Intermediate', 'Expert'];

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _titleController.dispose();
    _descController.dispose();
    _durationController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  Future<void> _pickDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      final time = await showTimePicker(context: context, initialTime: TimeOfDay.now());
      if (time != null) {
        setState(() {
          _selectedDate = date;
          _selectedTime = time;
        });
      }
    }
  }

  void _showSnackbar(String message, [Color? color]) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message), backgroundColor: color ?? Colors.deepPurple));
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        _showSnackbar('You must be logged in to post.', Colors.red);
        setState(() => _isLoading = false);
        return;
      }
      final data = {
        'type': isOffer ? 'offer' : 'request',
        'title': _titleController.text.trim(),
        'description': _descController.text.trim(),
        'category': _selectedCategory,
        'duration': _durationController.text.trim(),
        'location': _locationController.text.trim(),
        'availability':
            _selectedDate != null && _selectedTime != null
                ? DateTime(
                  _selectedDate!.year,
                  _selectedDate!.month,
                  _selectedDate!.day,
                  _selectedTime!.hour,
                  _selectedTime!.minute,
                ).toIso8601String()
                : null,
        'exchange': _selectedExchange,
        'mode': _selectedMode,
        'level': _selectedLevel,
        'userId': user.uid,
        'userEmail': user.email,
        'createdAt': FieldValue.serverTimestamp(),
      };
      await FirebaseFirestore.instance.collection('posts').add(data);
      setState(() => _isLoading = false);
      _showSnackbar(isOffer ? 'Skill Offer Posted!' : 'Skill Request Submitted!', Colors.green);
      Navigator.of(context).pop();
    } catch (e) {
      setState(() => _isLoading = false);
      _showSnackbar('Failed to submit. Please try again.', Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Post Offer / Request'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ChoiceChip(
                    label: const Text('Offer Skill'),
                    selected: isOffer,
                    onSelected: (v) => setState(() => isOffer = true),
                    selectedColor: Colors.deepPurple,
                    labelStyle: TextStyle(color: isOffer ? Colors.white : Colors.deepPurple),
                  ),
                  const SizedBox(width: 12),
                  ChoiceChip(
                    label: const Text('Request Help'),
                    selected: !isOffer,
                    onSelected: (v) => setState(() => isOffer = false),
                    selectedColor: Colors.deepPurple,
                    labelStyle: TextStyle(color: !isOffer ? Colors.white : Colors.deepPurple),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Title',
                        prefixIcon: Icon(Icons.title),
                        hintText: 'e.g. Learn Python in 10 Days',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.trim().isEmpty ? 'Title required' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _descController,
                      minLines: 3,
                      maxLines: 5,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        prefixIcon: Icon(Icons.description),
                        hintText: 'Describe what you offer or want to learn',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.trim().isEmpty ? 'Description required' : null,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedCategory,
                      items: _categories.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                      onChanged: (v) => setState(() => _selectedCategory = v),
                      decoration: const InputDecoration(
                        labelText: 'Skill Category',
                        prefixIcon: Icon(Icons.category),
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null ? 'Select a category' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _durationController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Duration (minutes/hours)',
                        prefixIcon: Icon(Icons.timer),
                        hintText: 'e.g. 60 (for 1 hour)',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.trim().isEmpty ? 'Duration required' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: 'Location',
                        prefixIcon: Icon(Icons.location_on),
                        hintText: 'e.g. Online, City, or Address',
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null || v.trim().isEmpty ? 'Location required' : null,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: const Icon(Icons.calendar_today),
                      title: Text(
                        _selectedDate == null || _selectedTime == null
                            ? 'Select Availability'
                            : '${DateFormat.yMMMd().format(_selectedDate!)} at ${_selectedTime!.format(context)}',
                      ),
                      trailing: ElevatedButton(onPressed: _pickDateTime, child: const Text('Pick')),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedExchange,
                      items: _exchangeOptions.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                      onChanged: (v) => setState(() => _selectedExchange = v),
                      decoration: const InputDecoration(
                        labelText: 'Exchange Preference',
                        prefixIcon: Icon(Icons.swap_horiz),
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null ? 'Select exchange type' : null,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedMode,
                      items: _modes.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                      onChanged: (v) => setState(() => _selectedMode = v),
                      decoration: const InputDecoration(
                        labelText: 'Mode',
                        prefixIcon: Icon(Icons.laptop),
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null ? 'Select mode' : null,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: _selectedLevel,
                      items: _levels.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                      onChanged: (v) => setState(() => _selectedLevel = v),
                      decoration: const InputDecoration(
                        labelText: 'Experience Level',
                        prefixIcon: Icon(Icons.star),
                        border: OutlineInputBorder(),
                      ),
                      validator: (v) => v == null ? 'Select level' : null,
                    ),
                    const SizedBox(height: 24),
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: ElevatedButton.icon(
                            icon: Icon(isOffer ? Icons.school : Icons.help_outline),
                            label: Text(isOffer ? 'Post Offer' : 'Post Request'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.deepPurple,
                              foregroundColor: Colors.white,
                              textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            onPressed: _submit,
                          ),
                        ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
